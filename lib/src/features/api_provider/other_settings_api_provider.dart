import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/data/network/handle_errors.dart';
import '../bloc/auth_blok.dart';
import '../response/generalResponse.dart';
import '../response/other_settings_response.dart';

class OtherSettingsApiProvider {
  final String _endpoint = AppConstants.endpoint;
  final Dio _dio = Dio();

  Future<OtherSettingsResponse> getfeatures(
      [int? page, int? size, String? key, String? cat]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      if (page != null) {
        url =
            '${_endpoint}getfeatures?page=$page&size=$size&key=$key&category=8';
      } else {
        url = '${_endpoint}getfeatures';
      }
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("ressssssssponsssssssssse");
      print(response);
      return OtherSettingsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return OtherSettingsResponse.withError(handleError(error));
    }
  }

  Future<OtherSettingsResponse> getCarBrands(
      [int? page, int? size, String? key]) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      if (page != null) {
        url =
            '${_endpoint}getbrands?page=$page&size=$size&key=$key&category=${AppConstants.carRentalsId}';
      } else {
        url = '${_endpoint}getbrands?category=${AppConstants.carRentalsId}';
      }
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return OtherSettingsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return OtherSettingsResponse.withError(handleError(error));
    }
  }

  Future<OtherSettingsResponse> getLocations() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getlocations';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return OtherSettingsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return OtherSettingsResponse.withError(handleError(error));
    }
  }

  Future<OtherSettingsResponse> getAdminFeatures(String category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url;
      url = '${_endpoint}getfeatures?category=$category';
      // url = _endpoint + 'getfeaturesAdmin?category=$category';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("getAdminFeatures");
      print(response);
      return OtherSettingsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return OtherSettingsResponse.withError(handleError(error));
    }
  }

  Future<GeneralGetResponse?> getfeaturebyid(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}getfeatures?id=$id';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return GeneralGetResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralGetResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addfeature(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}addfeatures',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> editfeature(
    FormData data,
  ) async {
    print("===============================================");

    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);

      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print({
        data.fields[0].key: data.fields[0].value,
        data.fields[1].key: data.fields[1].value,
        data.fields[2].key: data.fields[2].value,
        data.fields[3].key: data.fields[3].value,
      });
      Response response = await _dio.post('${_endpoint}updatefeature',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // contentType: "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deletefeature(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      Response response = await _dio.delete('${_endpoint}deletefeature?id=$id',
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<OtherSettingsResponse> gettypes(
      int? page, int? size, String? key, String? category) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      print("categorycategorycategorycategorycategorycategory");
      print(category);
      String url =
          '${_endpoint}gettypes?page=$page&size=$size&key=$key&category=8';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return OtherSettingsResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return OtherSettingsResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> addtype(FormData data) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print('FormData ${data.fields.map((e) => '${e.key}: ${e.value}')}');

      Response response = await _dio.post('${_endpoint}addtype',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> edittype(
    FormData data,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      print(data);
      Response response = await _dio.post('${_endpoint}updatetype',
          data: data,
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralResponse> deletetype(
    int id,
  ) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');

      print("profile");

      print(token);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';

      Response response = await _dio.delete('${_endpoint}deletetype?id=$id',
          options: Options(
              headers: {"Content-Type": "application/json"},
              // "Content-Type": "application/x-www-form-urlencoded",
              followRedirects: false,
              validateStatus: (status) {
                return status! < 600;
              }));
      print("qqqqqqq");
      print(response);
      return GeneralResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralResponse.withError(handleError(error));
    }
  }

  Future<GeneralGetResponse> gettypebyid(int id) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      var token = _prefs.getString('token');
      var userid = _prefs.getInt('user_id');
      print("token");
      print(userid);
      _dio.options.headers['Authorization'] = 'Bearer ${token!}';
      _dio.options.headers['lang'] = AuthBloc.isEnglish ? 'en' : 'ar';
      String url = '${_endpoint}gettypes?id=$id';
      print(url);
      Response response = await _dio.get(
        url,
        options: Options(
            headers: {"Content-Type": "application/x-www-form-urlencoded"},
            // "Content-Type": "application/x-www-form-urlencoded",
            followRedirects: false,
            validateStatus: (status) {
              return status! < 600;
            }),
      );
      print("qqqqqqq");
      print(response);
      return GeneralGetResponse.fromJson(response.data);
    } catch (error, stacktrace) {
      print("Exception occured: $error stackTrace: $stacktrace");
      return GeneralGetResponse.withError(handleError(error));
    }
  }
}
