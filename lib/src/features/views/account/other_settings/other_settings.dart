import 'package:admin_dubai/generated/l10n.dart';
import 'package:flutter/material.dart';

import '../../../../core/utils/resources.dart';
import '../widgets/other_settings_list_item.dart';
import 'account_information/account_information.dart';
import 'areas/areas.dart';
import 'configration/configuration.dart';
import 'contact_us/contact_us.dart';
import 'features/features.dart';
import 'price_plans/price_plans.dart';
import 'property_status/property_status.dart';
import 'types/types.dart';

class OtherSettings extends StatefulWidget {
  const OtherSettings({super.key});

  @override
  _OtherSettings createState() => _OtherSettings();
}

class _OtherSettings extends State<OtherSettings> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(S.of(context).Othersettigs),
            ),
            body: Container(
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const Features()));
                        },
                        text: S.of(context).Features),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const Types()));
                        },
                        text: S.of(context).Types),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const PropertyStatus()));
                        },
                        text: 'Property Status'),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  Accountinformation()));
                        },
                        text: S.of(context).AdminAccountInformation),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  Configration()));
                        },
                        text: S.of(context).Configuration),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (BuildContext context) =>
                                  const Contactus()));
                        },
                        text: S.of(context).contact),
                    const SizedBox(
                      height: 10,
                    ),
                    OtherSettingsListItem(
                        onTap: () {
                          Navigator.push(context,
                              MaterialPageRoute(builder: (context) {
                            return const Areas();
                          }));
                        },
                        text: S.of(context).Areas),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            )));
  }
}
