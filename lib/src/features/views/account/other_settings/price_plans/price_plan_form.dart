import 'dart:convert';

import 'package:admin_dubai/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/other_settings_api_provider.dart';
import '../../../../models/price_plan_model.dart';
import '../../../../response/generalResponse.dart';

class PricePlanForm extends StatefulWidget {
  final PricePlanModel? pricePlan;
  
  const PricePlanForm({super.key, this.pricePlan});

  @override
  State<PricePlanForm> createState() => _PricePlanFormState();
}

class _PricePlanFormState extends State<PricePlanForm> {
  bool isactions = false;
  bool loading = false;
  bool isLoading = false;

  TextEditingController nameEnController = TextEditingController();
  TextEditingController nameArController = TextEditingController();
  TextEditingController descriptionEnController = TextEditingController();
  TextEditingController descriptionArController = TextEditingController();
  
  List<PricePlanDataItem> dataItems = [];
  OtherSettingsApiProvider provider = OtherSettingsApiProvider();

  @override
  void initState() {
    super.initState();
    if (widget.pricePlan != null) {
      // Edit mode - populate fields
      nameEnController.text = widget.pricePlan!.nameEn ?? "";
      nameArController.text = widget.pricePlan!.nameAr ?? "";
      descriptionEnController.text = widget.pricePlan!.descriptionEn ?? "";
      descriptionArController.text = widget.pricePlan!.descriptionAr ?? "";
      dataItems = List.from(widget.pricePlan!.data ?? []);
    }
    
    // Add at least one data item if empty
    if (dataItems.isEmpty) {
      dataItems.add(const PricePlanDataItem());
    }
  }

  bool get isEditMode => widget.pricePlan != null;

  void addDataItem() {
    setState(() {
      dataItems.add(const PricePlanDataItem());
    });
  }

  void removeDataItem(int index) {
    if (dataItems.length > 1) {
      setState(() {
        dataItems.removeAt(index);
      });
    }
  }

  submit(FormData data) async {
    if (nameEnController.text.isEmpty) {
      snackbar(S.of(context).enterenn);
      return;
    }
    if (nameArController.text.isEmpty) {
      snackbar(S.of(context).enteraran);
      return;
    }

    setState(() {
      isLoading = true;
    });

    GeneralResponse successInformation;
    if (isEditMode) {
      successInformation = await provider.updatePricePlan(data);
    } else {
      successInformation = await provider.addPricePlan(data);
    }

    if (successInformation.code == 1) {
      Navigator.pop(context);
    } else {
      if (successInformation.msg == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation.msg!);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  void deletePricePlan() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(height: 20),
                      const Center(
                          child: Text(
                        'Delete Price Plan',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      )),
                      Container(
                        padding: const EdgeInsets.all(15),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20),
                                const Text('Are you sure you want to delete this price plan?'),
                                const SizedBox(height: 20),
                                StatefulBuilder(
                                  builder: (BuildContext context, setStateDelete) {
                                    return Center(
                                      child: GestureDetector(
                                        onTap: () async {
                                          setStateDelete(() {
                                            loading = true;
                                          });

                                          final successinformation =
                                              await provider.deletePricePlan(
                                                  widget.pricePlan!.id!);

                                          setStateDelete(() {
                                            loading = false;
                                          });
                                          if (successinformation.code == 1) {
                                            Navigator.pop(context);
                                            Navigator.pop(context, true);
                                          } else {
                                            Navigator.pop(context, true);
                                            snackbar(successinformation.msg ?? 
                                                'Cannot delete because it is being used');
                                          }
                                        },
                                        child: Container(
                                          height: 50,
                                          width: MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: const Color(0xffE04E4D),
                                              borderRadius:
                                                  BorderRadius.circular(10)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: loading
                                                      ? const ADLinearProgressIndicator()
                                                      : Text(
                                                          S.of(context).yesde,
                                                          style: const TextStyle(
                                                              color: Colors.white),
                                                        ))),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            ));
  }

  Widget buildDataItemForm(int index) {
    final item = dataItems[index];
    final dateController = TextEditingController(text: item.date ?? '');
    final orderController = TextEditingController(text: item.order ?? '');
    final paymentController = TextEditingController(text: item.payment?.toString() ?? '');
    final installmentController = TextEditingController(text: item.installment ?? '');

    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Plan ${index + 1}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (dataItems.length > 1)
                IconButton(
                  onPressed: () => removeDataItem(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                ),
            ],
          ),
          const SizedBox(height: 10),
          
          // Date field
          const Text('Date', style: TextStyle(fontSize: 13)),
          const SizedBox(height: 5),
          Container(
            height: 45,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white),
            child: TextFormField(
              controller: dateController,
              onChanged: (value) {
                dataItems[index] = PricePlanDataItem(
                  date: value,
                  order: item.order,
                  payment: item.payment,
                  installment: item.installment,
                );
              },
              decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10),
                  hintText: 'Enter date',
                  hintStyle: TextStyle(color: Color(0xffB7B7B7), fontSize: 14)),
            ),
          ),
          const SizedBox(height: 10),
          
          // Order field
          const Text('Order', style: TextStyle(fontSize: 13)),
          const SizedBox(height: 5),
          Container(
            height: 45,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white),
            child: TextFormField(
              controller: orderController,
              onChanged: (value) {
                dataItems[index] = PricePlanDataItem(
                  date: item.date,
                  order: value,
                  payment: item.payment,
                  installment: item.installment,
                );
              },
              decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10),
                  hintText: 'Enter order',
                  hintStyle: TextStyle(color: Color(0xffB7B7B7), fontSize: 14)),
            ),
          ),
          const SizedBox(height: 10),
          
          // Payment field
          const Text('Payment (%)', style: TextStyle(fontSize: 13)),
          const SizedBox(height: 5),
          Container(
            height: 45,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white),
            child: TextFormField(
              controller: paymentController,
              keyboardType: TextInputType.number,
              onChanged: (value) {
                dataItems[index] = PricePlanDataItem(
                  date: item.date,
                  order: item.order,
                  payment: int.tryParse(value),
                  installment: item.installment,
                );
              },
              decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10),
                  hintText: 'Enter payment percentage',
                  hintStyle: TextStyle(color: Color(0xffB7B7B7), fontSize: 14)),
            ),
          ),
          const SizedBox(height: 10),
          
          // Installment field
          const Text('Installment', style: TextStyle(fontSize: 13)),
          const SizedBox(height: 5),
          Container(
            height: 45,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 0.5, color: Colors.grey[300]!),
                color: Colors.white),
            child: TextFormField(
              controller: installmentController,
              onChanged: (value) {
                dataItems[index] = PricePlanDataItem(
                  date: item.date,
                  order: item.order,
                  payment: item.payment,
                  installment: value,
                );
              },
              decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10),
                  hintText: 'Enter installment info',
                  hintStyle: TextStyle(color: Color(0xffB7B7B7), fontSize: 14)),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(isEditMode ? 'Edit Price Plan' : 'Add Price Plan'),
              actions: isEditMode ? [
                Container(
                    padding: const EdgeInsets.only(left: 10, right: 10),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          isactions = !isactions;
                        });
                      },
                      child: const Icon(
                        Icons.more_horiz,
                        color: Colors.white,
                      ),
                    ))
              ] : null,
            ),
            body: Stack(children: [
              Container(
                padding: const EdgeInsets.all(20),
                child: ListView(
                  children: [
                    // Name fields
                    Text(
                      S.of(context).enterenn,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: nameEnController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).enterenn,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      S.of(context).enteraran,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: nameArController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).enteraran,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(height: 20),
                    
                    // Description fields
                    const Text(
                      'Description (English)',
                      style: TextStyle(fontSize: 13),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      height: 80,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: descriptionEnController,
                            maxLines: 3,
                            decoration: const InputDecoration(
                                border: InputBorder.none,
                                hintText: 'Enter description in English',
                                hintStyle: TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'Description (Arabic)',
                      style: TextStyle(fontSize: 13),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      height: 80,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: descriptionArController,
                            maxLines: 3,
                            decoration: const InputDecoration(
                                border: InputBorder.none,
                                hintText: 'Enter description in Arabic',
                                hintStyle: TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(height: 30),
                    
                    // Payment Plans section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Payment Plans',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        ElevatedButton.icon(
                          onPressed: addDataItem,
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Add More'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: GlobalColors.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    
                    // Data items
                    ...dataItems.asMap().entries.map((entry) {
                      return buildDataItemForm(entry.key);
                    }).toList(),
                    
                    const SizedBox(height: 40),
                    !isLoading
                        ? SizedBox(
                            height: 60,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Center(
                                    child: GestureDetector(
                                        onTap: () async {
                                          FormData form = FormData.fromMap({
                                            'name_en': nameEnController.text,
                                            'name_ar': nameArController.text,
                                            'description_en': descriptionEnController.text,
                                            'description_ar': descriptionArController.text,
                                            'data': jsonEncode(dataItems.map((e) => e.toJson()).toList()),
                                            if (isEditMode) 'id': widget.pricePlan!.id,
                                          });

                                          submit(form);
                                        },
                                        child: Container(
                                          height: 50,
                                          width: MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: GlobalColors.primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: Text(
                                                isEditMode ? S.of(context).SaveChanges : 'Add Price Plan',
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ))),
                                        )))
                              ],
                            ))
                        : const ADLinearProgressIndicator()
                  ],
                ),
              ),
              if (isEditMode && isactions)
                Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    color: Colors.grey.withValues(alpha: 0.4),
                    child: Container(
                        padding: const EdgeInsets.only(
                          left: 10,
                          right: 10,
                          bottom: 20,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.grey[100]!,
                              ),
                              width: MediaQuery.of(context).size.width,
                              child: Column(
                                children: [
                                  const SizedBox(height: 20),
                                  GestureDetector(
                                      onTap: () {
                                        deletePricePlan();
                                      },
                                      child: const Text(
                                        'Delete Price Plan',
                                        style: TextStyle(
                                            color: Color(0xffE04E4D),
                                            fontSize: 18),
                                      )),
                                  const SizedBox(height: 20),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),
                            GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isactions = !isactions;
                                  });
                                },
                                child: Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: Colors.white,
                                  ),
                                  width: MediaQuery.of(context).size.width,
                                  child: Center(
                                    child: Text(
                                      S.of(context).Cancel,
                                      style: const TextStyle(
                                          color: Color(0xff007AFF),
                                          fontSize: 18),
                                    ),
                                  ),
                                ))
                          ],
                        )))
            ])));
  }
}
