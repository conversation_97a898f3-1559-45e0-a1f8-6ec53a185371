import '../api_provider/other_settings_api_provider.dart';
import '../response/other_settings_response.dart';

class OtherSettingsRepository {
  OtherSettingsApiProvider _apiProvider = OtherSettingsApiProvider();
  Future<OtherSettingsResponse> getfeatures(
          int? page, int? size, String? key, String? cat) =>
      _apiProvider.getfeatures(page, size, key, cat);
  Future<OtherSettingsResponse> getCarBrands(
          int? page, int? size, String? key) =>
      _apiProvider.getCarBrands(page, size, key);
  Future<OtherSettingsResponse> getLocations() => _apiProvider.getLocations();
  Future<OtherSettingsResponse> getAdminFeatures(String? category) =>
      _apiProvider.getAdminFeatures(category!);
  Future<OtherSettingsResponse> gettypes(
          int? page, int? size, String? key, String? category) =>
      _apiProvider.gettypes(page, size, key, category!);
  // Future<GeneralResponse> addarea(FormData data) =>
  //     _apiProvider.addarea(data);
}
